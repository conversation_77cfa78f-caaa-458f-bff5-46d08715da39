using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

namespace RectangularPanelTool
{
    /// <summary>
    /// 排板方向枚举
    /// </summary>
    public enum PanelDirection
    {
        /// <summary>
        /// 从左往右排版
        /// </summary>
        LeftToRight,

        /// <summary>
        /// 从右往左排版
        /// </summary>
        RightToLeft,

        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        CenterToSides
    }

    /// <summary>
    /// 参数验证异常
    /// </summary>
    public class InvalidParameterException : Exception
    {
        public InvalidParameterException(string message) : base(message) { }
        public InvalidParameterException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 几何计算异常
    /// </summary>
    public class GeometryCalculationException : Exception
    {
        public GeometryCalculationException(string message) : base(message) { }
        public GeometryCalculationException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class PanelGenerator
    {
        #region 常量定义
        private const double TOLERANCE = 1e-9;
        private const double MIN_PANEL_HEIGHT = 1.0;
        private const int MAX_ITERATIONS = 10000;
        private const double MIN_PANEL_WIDTH = 0.1;
        #endregion

        #region 私有字段
        private Document _doc;
        private Editor _ed;
        private Database _db;
        private ObjectId _outlineId;
        private List<ObjectId> _windowIds;
        private List<ObjectId> _doorIds;
        private double _startDistance;
        private double _panelWidth;
        private double _topGap;
        private double _bottomGap;
        private double _windowGap;
        private double _doorTopGap;
        private PanelDirection _direction;
        #endregion

        #region 属性
        public bool HasOutline => !_outlineId.IsNull;
        public int WindowCount => _windowIds?.Count ?? 0;
        public int DoorCount => _doorIds?.Count ?? 0;

        public double StartDistance
        {
            get => _startDistance;
            set => _startDistance = value;
        }

        public double PanelWidth
        {
            get => _panelWidth;
            set
            {
                if (value <= MIN_PANEL_WIDTH)
                    throw new InvalidParameterException($"板宽必须大于{MIN_PANEL_WIDTH}");
                _panelWidth = value;
            }
        }

        public double TopGap
        {
            get => _topGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("顶部间隙不能为负数");
                _topGap = value;
            }
        }

        public double BottomGap
        {
            get => _bottomGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("底部间隙不能为负数");
                _bottomGap = value;
            }
        }

        public double WindowGap
        {
            get => _windowGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("窗户间隙不能为负数");
                _windowGap = value;
            }
        }

        public double DoorTopGap
        {
            get => _doorTopGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("门顶部间隙不能为负数");
                _doorTopGap = value;
            }
        }

        public PanelDirection Direction { get => _direction; set => _direction = value; }
        #endregion

        #region 构造函数
        public PanelGenerator(Document doc)
        {
            _doc = doc ?? throw new ArgumentNullException(nameof(doc));
            _ed = doc.Editor;
            _db = doc.Database;
            _outlineId = ObjectId.Null;
            _windowIds = new List<ObjectId>();
            _doorIds = new List<ObjectId>();

            // 设置默认值
            _panelWidth = 1000;
            _topGap = 5;
            _bottomGap = 10;
            _windowGap = 5;
            _doorTopGap = 5;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 生成矩形板
        /// </summary>
        /// <returns>成功生成的矩形板数量</returns>
        public int GeneratePanels()
        {
            ValidateParameters();

            if (_outlineId.IsNull)
            {
                _ed.WriteMessage("\n请先选择轮廓线!");
                return 0;
            }

            // 对于中心向两边排版，需要先获取中心点
            Point3d? centerPoint = null;
            if (_direction == PanelDirection.CenterToSides)
            {
                PromptPointResult result = _ed.GetPoint("\n请选择中心点: ");
                if (result.Status != PromptStatus.OK)
                {
                    return 0;
                }
                centerPoint = result.Value;
            }

            int panelCount = 0;

            using (Transaction tr = _db.TransactionManager.StartTransaction())
            {
                try
                {
                    // 获取轮廓线
                    Polyline outline = tr.GetObject(_outlineId, OpenMode.ForRead) as Polyline;
                    if (outline == null)
                    {
                        _ed.WriteMessage("\n无法打开轮廓线!");
                        tr.Commit();
                        return 0;
                    }

                    // 如果轮廓线不是闭合的，则创建一个临时的闭合副本
                    Polyline workingOutline = outline;
                    bool needsDisposal = false;
                    if (!outline.Closed)
                    {
                        _ed.WriteMessage("\n自动闭合轮廓线进行处理...");
                        workingOutline = outline.Clone() as Polyline;
                        workingOutline.Closed = true;
                        needsDisposal = true;
                    }

                    try
                    {
                        // 获取轮廓线的边界盒，仅用于确定X方向范围
                        Extents3d outlineExtents = workingOutline.GeometricExtents;
                        double minX = outlineExtents.MinPoint.X;
                        double maxX = outlineExtents.MaxPoint.X;

                        // 加载门窗洞口
                        List<Extents3d> windowExtents = LoadWindowExtents(tr);
                        List<Extents3d> doorExtents = LoadDoorExtents(tr);

                        // 创建块表记录
                        BlockTable bt = tr.GetObject(_db.BlockTableId, OpenMode.ForRead) as BlockTable;
                        BlockTableRecord ms = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                        // 根据不同排版方向生成板子
                        switch (_direction)
                        {
                            case PanelDirection.LeftToRight:
                                panelCount += GenerateLeftToRightPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr);
                                break;
                            case PanelDirection.RightToLeft:
                                panelCount += GenerateRightToLeftPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr);
                                break;
                            case PanelDirection.CenterToSides:
                                if (centerPoint.HasValue)
                                {
                                    panelCount += GenerateCenterToSidesPanels(workingOutline, minX, maxX, windowExtents, doorExtents, ms, tr, centerPoint.Value.X);
                                }
                                break;
                        }
                    }
                    finally
                    {
                        // 如果创建了临时副本，应当释放资源
                        if (needsDisposal && workingOutline != null)
                        {
                            workingOutline.Dispose();
                        }
                    }

                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    _ed.WriteMessage("\n生成矩形板时出错: {0}", ex.Message);
                    tr.Abort();
                    return 0;
                }
            }

            return panelCount;
        }

        /// <summary>
        /// 验证参数有效性
        /// </summary>
        private void ValidateParameters()
        {
            if (_panelWidth <= MIN_PANEL_WIDTH)
                throw new InvalidParameterException($"板宽必须大于{MIN_PANEL_WIDTH}");

            if (_topGap < 0 || _bottomGap < 0 || _windowGap < 0 || _doorTopGap < 0)
                throw new InvalidParameterException("间隙值不能为负数");
        }

        /// <summary>
        /// 加载窗洞口范围
        /// </summary>
        private List<Extents3d> LoadWindowExtents(Transaction tr)
        {
            List<Extents3d> windowExtents = new List<Extents3d>();
            foreach (ObjectId windowId in _windowIds)
            {
                if (!windowId.IsNull)
                {
                    Polyline window = tr.GetObject(windowId, OpenMode.ForRead) as Polyline;
                    if (window != null && window.Closed)
                    {
                        windowExtents.Add(window.GeometricExtents);
                    }
                }
            }
            return windowExtents;
        }

        /// <summary>
        /// 加载门洞口范围
        /// </summary>
        private List<Extents3d> LoadDoorExtents(Transaction tr)
        {
            List<Extents3d> doorExtents = new List<Extents3d>();
            foreach (ObjectId doorId in _doorIds)
            {
                if (!doorId.IsNull)
                {
                    Polyline door = tr.GetObject(doorId, OpenMode.ForRead) as Polyline;
                    if (door != null && !door.Closed)
                    {
                        doorExtents.Add(door.GeometricExtents);
                    }
                }
            }
            return doorExtents;
        }
        #endregion

        #region 私有排版方法
        /// <summary>
        /// 从左往右排版
        /// </summary>
        private int GenerateLeftToRightPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;
            int iterations = 0;

            // 确定起始点和结束点
            double startX = minX + _startDistance;
            double endX = maxX;

            // 计算当前X位置
            double currentX = startX;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX < endX || (_startDistance < 0 && currentX < minX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentX, nextX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX >= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 从右往左排版
        /// </summary>
        private int GenerateRightToLeftPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;
            int iterations = 0;

            // 确定起始点
            double startX = maxX - _startDistance;
            double endX = minX;

            // 计算当前X位置
            double currentX = startX;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX > endX || (_startDistance < 0 && currentX > maxX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentX, nextX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX <= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        private int GenerateCenterToSidesPanels(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr, double centerX)
        {
            int panelCount = 0;
            int leftIterations = 0;
            int rightIterations = 0;

            // 确定起始位置
            double leftStartX, rightStartX;

            if (_startDistance >= 0)
            {
                // 正常起始距离，向内缩进
                leftStartX = centerX - _startDistance / 2;
                rightStartX = centerX + _startDistance / 2;
            }
            else
            {
                // 负值起始距离，向外扩展
                leftStartX = centerX + _startDistance / 2;  // 向左扩展（负值的一半）
                rightStartX = centerX - _startDistance / 2; // 向右扩展（负值的一半变为正值）
            }

            // 设置当前位置为起始位置
            double currentLeftX = leftStartX;
            double currentRightX = rightStartX;

            // 向左生成板子，添加循环保护
            while ((currentLeftX > minX || (_startDistance < 0 && currentLeftX > centerX + _startDistance)) && leftIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextLeftX = currentLeftX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentLeftX, nextLeftX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentLeftX = nextLeftX;
                leftIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentLeftX <= minX && _startDistance >= 0)
                {
                    break;
                }
            }

            // 向右生成板子，添加循环保护
            while ((currentRightX < maxX || (_startDistance < 0 && currentRightX < centerX - _startDistance)) && rightIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextRightX = currentRightX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(outline, currentRightX, nextRightX, windowExtents, doorExtents, ms, tr);

                // 更新当前X位置
                currentRightX = nextRightX;
                rightIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentRightX >= maxX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (leftIterations >= MAX_ITERATIONS || rightIterations >= MAX_ITERATIONS)
            {
                _ed.WriteMessage($"\n警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整");
            }

            return panelCount;
        }
        
        /// <summary>
        /// 在指定位置生成板子
        /// </summary>
        private int GeneratePanelAtPosition(Polyline outline, double minX, double maxX,
            List<Extents3d> windowExtents, List<Extents3d> doorExtents,
            BlockTableRecord ms, Transaction tr)
        {
            int panelCount = 0;

            double panelMinX = Math.Min(minX, maxX);
            double panelMaxX = Math.Max(minX, maxX);

            // 计算当前X位置下，轮廓线的实际最高和最低点
            Tuple<double, double> yRange = GetOutlineYRangeAtXrange(outline, panelMinX, panelMaxX);
            if (yRange == null)
            {
                // 如果在此X位置没有有效的Y范围，则跳过
                return 0;
            }

            // 应用间隙
            double minY = yRange.Item1 + _bottomGap;

            // 修改：分别获取左右两侧边缘的Y坐标，取较小值确保矩形板顶部不超出轮廓线
            // 左侧边缘Y坐标
            double leftEdgeY = GetYCoordinateAtX(outline, panelMinX);
            // 右侧边缘Y坐标
            double rightEdgeY = GetYCoordinateAtX(outline, panelMaxX);

            // 检查是否获取到有效的Y坐标
            if (Math.Abs(leftEdgeY - double.MinValue) < TOLERANCE || Math.Abs(rightEdgeY - double.MinValue) < TOLERANCE)
            {
                // 如果无法获取有效的Y坐标，跳过此位置
                return 0;
            }

            // 取两者的较小值作为矩形板顶部的Y坐标
            double topY = Math.Min(leftEdgeY, rightEdgeY) - _topGap;

            // 检查是否需要对板进行分段(遇到门窗)
            List<Tuple<double, double>> yRanges = new List<Tuple<double, double>>();
            yRanges.Add(new Tuple<double, double>(minY, topY));

            // 性能优化：只处理与当前板子X范围相交的洞口
            List<Extents3d> relevantWindows = GetIntersectingHoles(windowExtents, panelMinX, panelMaxX);
            List<Extents3d> relevantDoors = GetIntersectingHoles(doorExtents, panelMinX, panelMaxX);

            // 检查是否有部分重叠的洞口需要生成非矩形板材
            bool hasPartialOverlap = false;
            List<Extents3d> partialOverlapWindows = new List<Extents3d>();
            List<Extents3d> partialOverlapDoors = new List<Extents3d>();

            // 处理窗户
            foreach (Extents3d window in relevantWindows)
            {
                OverlapType overlapType = GetOverlapType(panelMinX, panelMaxX, window.MinPoint.X, window.MaxPoint.X);

                if (overlapType == OverlapType.CompletelyInside)
                {
                    // 原有逻辑：板材完全在洞口内，分割Y范围
                    SplitYRanges(ref yRanges, window.MinPoint.Y - _windowGap, window.MaxPoint.Y + _windowGap);
                }
                else if (overlapType != OverlapType.None)
                {
                    // 新逻辑：部分重叠，需要生成非矩形板材
                    hasPartialOverlap = true;
                    partialOverlapWindows.Add(window);
                }
            }

            // 处理门
            foreach (Extents3d door in relevantDoors)
            {
                OverlapType overlapType = GetOverlapType(panelMinX, panelMaxX, door.MinPoint.X, door.MaxPoint.X);

                if (overlapType == OverlapType.CompletelyInside)
                {
                    // 原有逻辑：板材完全在洞口内，分割Y范围
                    SplitYRanges(ref yRanges, minY, door.MaxPoint.Y + _doorTopGap);
                }
                else if (overlapType != OverlapType.None)
                {
                    // 新逻辑：部分重叠，需要生成非矩形板材
                    hasPartialOverlap = true;
                    partialOverlapDoors.Add(door);
                }
            }

            // 如果有部分重叠的洞口，生成非矩形板材
            if (hasPartialOverlap)
            {
                // 为每个Y范围创建可能的非矩形板材
                foreach (var range in yRanges)
                {
                    // 确保高度足够
                    if (range.Item2 - range.Item1 > MIN_PANEL_HEIGHT)
                    {
                        // 合并所有相关洞口
                        List<Extents3d> allRelevantHoles = new List<Extents3d>();
                        allRelevantHoles.AddRange(partialOverlapWindows);
                        allRelevantHoles.AddRange(partialOverlapDoors);

                        // 生成窗户的非矩形板材
                        if (partialOverlapWindows.Count > 0)
                        {
                            PanelShape windowShape = GeneratePanelShape(panelMinX, panelMaxX, range.Item1, range.Item2,
                                partialOverlapWindows, true);
                            if (windowShape.Vertices.Count > 0)
                            {
                                if (CreatePanelEntity(windowShape, ms, tr))
                                {
                                    panelCount++;
                                }
                            }
                        }
                        // 生成门的非矩形板材
                        else if (partialOverlapDoors.Count > 0)
                        {
                            PanelShape doorShape = GeneratePanelShape(panelMinX, panelMaxX, range.Item1, range.Item2,
                                partialOverlapDoors, false);
                            if (doorShape.Vertices.Count > 0)
                            {
                                if (CreatePanelEntity(doorShape, ms, tr))
                                {
                                    panelCount++;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                // 原有逻辑：为每个Y范围创建标准矩形板
                foreach (var range in yRanges)
                {
                    // 确保高度足够
                    if (range.Item2 - range.Item1 > MIN_PANEL_HEIGHT)
                    {
                        try
                        {
                            Point2d pt1 = new Point2d(panelMinX, range.Item1);
                            Point2d pt2 = new Point2d(panelMaxX, range.Item1);
                            Point2d pt3 = new Point2d(panelMaxX, range.Item2);
                            Point2d pt4 = new Point2d(panelMinX, range.Item2);

                            // 创建矩形板
                            using (Polyline panel = new Polyline())
                            {
                                panel.AddVertexAt(0, pt1, 0, 0, 0);
                                panel.AddVertexAt(1, pt2, 0, 0, 0);
                                panel.AddVertexAt(2, pt3, 0, 0, 0);
                                panel.AddVertexAt(3, pt4, 0, 0, 0);
                                panel.Closed = true;

                                // 将板添加到模型空间
                                ms.AppendEntity(panel);
                                tr.AddNewlyCreatedDBObject(panel, true);
                                panelCount++;
                            }
                        }
                        catch (System.Exception ex)
                        {
                            _ed.WriteMessage($"\n创建矩形板时出错: {ex.Message}");
                        }
                    }
                }
            }

            return panelCount;
        }
        #endregion

        /// <summary>
        /// 获取轮廓线在指定X范围内的Y范围(最高点和最低点)
        /// </summary>
        private Tuple<double, double> GetOutlineYRangeAtXrange(Polyline outline, double minX, double maxX)
        {
            double minY = double.MaxValue;
            double maxY = double.MinValue;
            bool foundIntersection = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，更新Y范围
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        minY = Math.Min(minY, p1.Y);
                        maxY = Math.Max(maxY, p1.Y);
                        foundIntersection = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        minY = Math.Min(minY, p2.Y);
                        maxY = Math.Max(maxY, p2.Y);
                        foundIntersection = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        minY = Math.Min(minY, y);
                        maxY = Math.Max(maxY, y);
                        foundIntersection = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        minY = Math.Min(minY, y);
                        maxY = Math.Max(maxY, y);
                        foundIntersection = true;
                    }
                }
            }

            // 如果有交点，返回Y范围
            if (foundIntersection)
            {
                return new Tuple<double, double>(minY, maxY);
            }
            
            // 如果没有交点，检查X范围是否在轮廓内部
            Point3d testPoint = new Point3d((minX + maxX) / 2, outline.GeometricExtents.MinPoint.Y, 0);
            if (IsPointInOutline(testPoint, outline))
            {
                // 如果中点在轮廓内，寻找轮廓上方和下方的交点
                double top = double.MinValue;
                double bottom = double.MaxValue;
                
                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    int nextIdx = (i + 1) % outline.NumberOfVertices;
                    Point2d p1 = outline.GetPoint2dAt(i);
                    Point2d p2 = outline.GetPoint2dAt(nextIdx);
                    
                    // 检查线段是否跨越测试点的X坐标
                    if ((p1.X <= testPoint.X && p2.X >= testPoint.X) || 
                        (p1.X >= testPoint.X && p2.X <= testPoint.X))
                    {
                        // 计算线段在测试点X坐标处的Y值
                        if (p1.X == p2.X)
                        {
                            // 垂直线段
                            bottom = Math.Min(bottom, Math.Min(p1.Y, p2.Y));
                            top = Math.Max(top, Math.Max(p1.Y, p2.Y));
                        }
                        else
                        {
                            double t = (testPoint.X - p1.X) / (p2.X - p1.X);
                            double y = p1.Y + t * (p2.Y - p1.Y);
                            
                            // 如果线段在测试点上方，更新顶部值
                            if (y > testPoint.Y && y < bottom)
                            {
                                bottom = y;
                            }
                            // 如果线段在测试点下方，更新底部值
                            else if (y < testPoint.Y && y > top)
                            {
                                top = y;
                            }
                        }
                    }
                }
                
                if (top != double.MinValue && bottom != double.MaxValue)
                {
                    return new Tuple<double, double>(top, bottom);
                }
            }
            
            return null;
        }

        #region 几何计算辅助方法
        /// <summary>
        /// 检查点是否在轮廓内
        /// </summary>
        private bool IsPointInOutline(Point3d point, Polyline outline)
        {
            try
            {
                // 使用射线法检查点是否在多边形内部
                // 从该点向右发射一条射线，计算与多边形边的交点数量
                // 如果交点数为奇数，则点在多边形内部；如果为偶数，则点在外部

                int intersections = 0;

                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    // 获取当前线段的两个端点
                    int nextIndex = (i + 1) % outline.NumberOfVertices;
                    Point2d pt1 = outline.GetPoint2dAt(i);
                    Point2d pt2 = outline.GetPoint2dAt(nextIndex);

                    // 避免处理水平线段（可能导致精度问题）
                    if (Math.Abs(pt1.Y - pt2.Y) < TOLERANCE)
                        continue;

                    // 判断射线是否与线段相交
                    if ((pt1.Y > point.Y) != (pt2.Y > point.Y))
                    {
                        // 计算交点的X坐标
                        double intersectionX = (pt2.X - pt1.X) * (point.Y - pt1.Y) / (pt2.Y - pt1.Y) + pt1.X;

                        // 只计算在射线右侧的交点
                        if (point.X < intersectionX - TOLERANCE)
                        {
                            intersections++;
                        }
                    }
                }

                // 奇数次交叉表示点在多边形内部
                return (intersections % 2 == 1);
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("检查点是否在轮廓内时发生错误", ex);
            }
        }

        /// <summary>
        /// 判断两个X范围是否重叠
        /// </summary>
        private bool IsOverlappingHorizontally(double rect1MinX, double rect1MaxX, double rect2MinX, double rect2MaxX)
        {
            return (rect1MinX <= rect2MaxX && rect1MaxX >= rect2MinX);
        }

        /// <summary>
        /// 分割Y范围，避开洞口
        /// </summary>
        private void SplitYRanges(ref List<Tuple<double, double>> ranges, double yMin, double yMax)
        {
            if (ranges == null)
            {
                ranges = new List<Tuple<double, double>>();
                return;
            }

            // 确保yMin <= yMax
            if (yMin > yMax)
            {
                double temp = yMin;
                yMin = yMax;
                yMax = temp;
            }

            List<Tuple<double, double>> newRanges = new List<Tuple<double, double>>();

            foreach (var range in ranges)
            {
                if (range == null)
                    continue;

                double rangeMin = range.Item1;
                double rangeMax = range.Item2;

                // 确保范围有效
                if (rangeMax <= rangeMin + TOLERANCE)
                    continue;

                // 洞口在范围上方（使用容差比较）
                if (yMin >= rangeMax - TOLERANCE)
                {
                    newRanges.Add(range);
                    continue;
                }

                // 洞口在范围下方（使用容差比较）
                if (yMax <= rangeMin + TOLERANCE)
                {
                    newRanges.Add(range);
                    continue;
                }

                // 洞口与范围有重叠部分
                // 添加洞口下方的范围
                if (rangeMin < yMin - TOLERANCE)
                {
                    double bottomRangeTop = Math.Min(yMin, rangeMax);
                    if (bottomRangeTop - rangeMin > MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(rangeMin, bottomRangeTop));
                    }
                }

                // 添加洞口上方的范围
                if (rangeMax > yMax + TOLERANCE)
                {
                    double topRangeBottom = Math.Max(yMax, rangeMin);
                    if (rangeMax - topRangeBottom > MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(topRangeBottom, rangeMax));
                    }
                }
            }

            ranges = newRanges;
        }

        /// <summary>
        /// 判断矩形是否完全在水平方向上落在洞口范围内
        /// </summary>
        private bool IsRectCompletelyInsideHorizontally(double rectMinX, double rectMaxX, double holeMinX, double holeMaxX)
        {
            // 使用容差比较避免浮点精度问题
            return (rectMinX >= holeMinX - TOLERANCE) && (rectMaxX <= holeMaxX + TOLERANCE);
        }

        /// <summary>
        /// 板材与洞口的重叠类型
        /// </summary>
        private enum OverlapType
        {
            None,                    // 无重叠
            CompletelyInside,        // 板材完全在洞口内（原有逻辑）
            LeftOverlap,            // 板材左侧与洞口重叠
            RightOverlap,           // 板材右侧与洞口重叠
            HoleCompletelyInside,   // 洞口完全在板材内
            CompleteOverlap         // 完全重叠（板材和洞口范围相同）
        }

        /// <summary>
        /// 检测板材与洞口的重叠类型
        /// </summary>
        /// <param name="rectMinX">板材最小X坐标</param>
        /// <param name="rectMaxX">板材最大X坐标</param>
        /// <param name="holeMinX">洞口最小X坐标</param>
        /// <param name="holeMaxX">洞口最大X坐标</param>
        /// <returns>重叠类型</returns>
        private OverlapType GetOverlapType(double rectMinX, double rectMaxX, double holeMinX, double holeMaxX)
        {
            // 使用容差比较避免浮点精度问题
            bool rectLeftInHole = rectMinX >= holeMinX - TOLERANCE;
            bool rectRightInHole = rectMaxX <= holeMaxX + TOLERANCE;
            bool holeLeftInRect = holeMinX >= rectMinX - TOLERANCE;
            bool holeRightInRect = holeMaxX <= rectMaxX + TOLERANCE;

            // 检查是否有重叠
            if (rectMaxX < holeMinX - TOLERANCE || rectMinX > holeMaxX + TOLERANCE)
            {
                return OverlapType.None;
            }

            // 板材完全在洞口内
            if (rectLeftInHole && rectRightInHole)
            {
                return OverlapType.CompletelyInside;
            }

            // 洞口完全在板材内
            if (holeLeftInRect && holeRightInRect)
            {
                return OverlapType.HoleCompletelyInside;
            }

            // 检查完全重叠（范围基本相同）
            if (Math.Abs(rectMinX - holeMinX) < TOLERANCE && Math.Abs(rectMaxX - holeMaxX) < TOLERANCE)
            {
                return OverlapType.CompleteOverlap;
            }

            // 左侧重叠：板材左侧在洞口内，右侧在洞口外
            if (rectLeftInHole && !rectRightInHole)
            {
                return OverlapType.LeftOverlap;
            }

            // 右侧重叠：板材右侧在洞口内，左侧在洞口外
            if (!rectLeftInHole && rectRightInHole)
            {
                return OverlapType.RightOverlap;
            }

            // 其他情况视为无重叠
            return OverlapType.None;
        }

        /// <summary>
        /// 板材形状信息
        /// </summary>
        private class PanelShape
        {
            public List<Point2d> Vertices { get; set; }
            public bool IsRectangular { get; set; }

            public PanelShape()
            {
                Vertices = new List<Point2d>();
                IsRectangular = true;
            }
        }

        /// <summary>
        /// 根据板材与洞口的重叠情况生成板材形状
        /// </summary>
        /// <param name="panelMinX">板材最小X坐标</param>
        /// <param name="panelMaxX">板材最大X坐标</param>
        /// <param name="panelMinY">板材最小Y坐标</param>
        /// <param name="panelMaxY">板材最大Y坐标</param>
        /// <param name="holes">相关洞口列表</param>
        /// <param name="isWindow">是否为窗户洞口</param>
        /// <returns>板材形状信息</returns>
        private PanelShape GeneratePanelShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            List<Extents3d> holes, bool isWindow)
        {
            PanelShape shape = new PanelShape();

            // 如果没有洞口，生成标准矩形
            if (holes == null || holes.Count == 0)
            {
                shape.Vertices.Add(new Point2d(panelMinX, panelMinY));
                shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));
                shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));
                shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));
                return shape;
            }

            // 检查每个洞口的重叠类型
            List<Extents3d> overlappingHoles = new List<Extents3d>();
            foreach (var hole in holes)
            {
                OverlapType overlapType = GetOverlapType(panelMinX, panelMaxX, hole.MinPoint.X, hole.MaxPoint.X);
                if (overlapType != OverlapType.None)
                {
                    overlappingHoles.Add(hole);
                }
            }

            // 如果没有重叠洞口，生成标准矩形
            if (overlappingHoles.Count == 0)
            {
                shape.Vertices.Add(new Point2d(panelMinX, panelMinY));
                shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));
                shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));
                shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));
                return shape;
            }

            // 处理复杂情况：生成避开洞口的多边形
            return GenerateComplexPanelShape(panelMinX, panelMaxX, panelMinY, panelMaxY, overlappingHoles, isWindow);
        }

        /// <summary>
        /// 生成避开洞口的复杂板材形状
        /// </summary>
        private PanelShape GenerateComplexPanelShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            List<Extents3d> holes, bool isWindow)
        {
            PanelShape shape = new PanelShape();
            shape.IsRectangular = false;

            // 对于简单情况，先处理单个洞口
            if (holes.Count == 1)
            {
                return GenerateSingleHolePanelShape(panelMinX, panelMaxX, panelMinY, panelMaxY, holes[0], isWindow);
            }

            // 对于多个洞口的复杂情况，使用更复杂的算法
            // 这里先实现基本逻辑，后续可以扩展
            return GenerateMultiHolePanelShape(panelMinX, panelMaxX, panelMinY, panelMaxY, holes, isWindow);
        }

        /// <summary>
        /// 生成单个洞口情况下的板材形状
        /// </summary>
        private PanelShape GenerateSingleHolePanelShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            Extents3d hole, bool isWindow)
        {
            PanelShape shape = new PanelShape();
            shape.IsRectangular = false;

            OverlapType overlapType = GetOverlapType(panelMinX, panelMaxX, hole.MinPoint.X, hole.MaxPoint.X);
            double gap = isWindow ? _windowGap : _doorTopGap;

            switch (overlapType)
            {
                case OverlapType.LeftOverlap:
                    // 板材左侧与洞口重叠，去掉重叠部分，保留右侧部分
                    return GenerateLeftOverlapShape(panelMinX, panelMaxX, panelMinY, panelMaxY, hole, gap, isWindow);

                case OverlapType.RightOverlap:
                    // 板材右侧与洞口重叠，去掉重叠部分，保留左侧部分
                    return GenerateRightOverlapShape(panelMinX, panelMaxX, panelMinY, panelMaxY, hole, gap, isWindow);

                case OverlapType.HoleCompletelyInside:
                    // 洞口完全在板材内，生成中间有凹槽的形状
                    return GenerateHoleInsideShape(panelMinX, panelMaxX, panelMinY, panelMaxY, hole, gap, isWindow);

                case OverlapType.CompletelyInside:
                    // 板材完全在洞口内，这种情况应该分段处理，不生成形状
                    shape.Vertices.Clear();
                    return shape;

                default:
                    // 其他情况生成标准矩形
                    shape.IsRectangular = true;
                    shape.Vertices.Add(new Point2d(panelMinX, panelMinY));
                    shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));
                    shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));
                    shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));
                    return shape;
            }
        }

        /// <summary>
        /// 生成左侧重叠情况的板材形状（去掉左侧重叠部分，保留右侧）
        /// </summary>
        private PanelShape GenerateLeftOverlapShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            Extents3d hole, double gap, bool isWindow)
        {
            PanelShape shape = new PanelShape();
            shape.IsRectangular = true; // 去掉重叠部分后仍然是矩形

            // 计算洞口右边界（加上间隙）
            double holeRightEdge = hole.MaxPoint.X + gap;

            // 新的板材左边界应该从洞口右边界开始（避开重叠部分）
            double newPanelMinX = Math.Max(panelMinX, holeRightEdge);

            // 如果调整后的板材宽度太小，则不生成
            if (panelMaxX - newPanelMinX < MIN_PANEL_HEIGHT) // 使用最小高度作为最小宽度参考
            {
                shape.Vertices.Clear();
                return shape;
            }

            // 生成去掉左侧重叠部分的矩形板材（逆时针顶点顺序）
            shape.Vertices.Add(new Point2d(newPanelMinX, panelMinY));        // 左下
            shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));           // 右下
            shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));           // 右上
            shape.Vertices.Add(new Point2d(newPanelMinX, panelMaxY));        // 左上

            return shape;
        }

        /// <summary>
        /// 生成右侧重叠情况的板材形状（去掉右侧重叠部分，保留左侧）
        /// </summary>
        private PanelShape GenerateRightOverlapShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            Extents3d hole, double gap, bool isWindow)
        {
            PanelShape shape = new PanelShape();
            shape.IsRectangular = true; // 去掉重叠部分后仍然是矩形

            // 计算洞口左边界（减去间隙）
            double holeLeftEdge = hole.MinPoint.X - gap;

            // 新的板材右边界应该到洞口左边界为止（避开重叠部分）
            double newPanelMaxX = Math.Min(panelMaxX, holeLeftEdge);

            // 如果调整后的板材宽度太小，则不生成
            if (newPanelMaxX - panelMinX < MIN_PANEL_HEIGHT) // 使用最小高度作为最小宽度参考
            {
                shape.Vertices.Clear();
                return shape;
            }

            // 生成去掉右侧重叠部分的矩形板材（逆时针顶点顺序）
            shape.Vertices.Add(new Point2d(panelMinX, panelMinY));           // 左下
            shape.Vertices.Add(new Point2d(newPanelMaxX, panelMinY));        // 右下
            shape.Vertices.Add(new Point2d(newPanelMaxX, panelMaxY));        // 右上
            shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));           // 左上

            return shape;
        }

        /// <summary>
        /// 生成洞口完全在板材内部情况的形状（中间有凹槽）
        /// </summary>
        private PanelShape GenerateHoleInsideShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            Extents3d hole, double gap, bool isWindow)
        {
            PanelShape shape = new PanelShape();
            shape.IsRectangular = false;

            double holeMinX = hole.MinPoint.X - gap;
            double holeMaxX = hole.MaxPoint.X + gap;
            double holeMinY = hole.MinPoint.Y - gap;
            double holeMaxY = hole.MaxPoint.Y + gap;

            // 确保洞口范围在板材范围内
            holeMinX = Math.Max(holeMinX, panelMinX);
            holeMaxX = Math.Min(holeMaxX, panelMaxX);
            holeMinY = Math.Max(holeMinY, panelMinY);
            holeMaxY = Math.Min(holeMaxY, panelMaxY);

            // 如果是门，底部到地面
            if (!isWindow)
            {
                holeMinY = panelMinY;
            }

            // 生成"凸"字形状的顶点（逆时针，外轮廓）
            shape.Vertices.Add(new Point2d(panelMinX, panelMinY));           // 左下
            shape.Vertices.Add(new Point2d(panelMinX, holeMinY));            // 左下到凹槽底部
            shape.Vertices.Add(new Point2d(holeMinX, holeMinY));             // 凹槽底部左侧
            shape.Vertices.Add(new Point2d(holeMinX, holeMaxY));             // 凹槽顶部左侧
            shape.Vertices.Add(new Point2d(panelMinX, holeMaxY));            // 凹槽顶部到左边
            shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));           // 左上
            shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));           // 右上
            shape.Vertices.Add(new Point2d(panelMaxX, holeMaxY));            // 右上到凹槽顶部
            shape.Vertices.Add(new Point2d(holeMaxX, holeMaxY));             // 凹槽顶部右侧
            shape.Vertices.Add(new Point2d(holeMaxX, holeMinY));             // 凹槽底部右侧
            shape.Vertices.Add(new Point2d(panelMaxX, holeMinY));            // 凹槽底部到右边
            shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));           // 右下

            return shape;
        }

        /// <summary>
        /// 生成多个洞口情况的板材形状（简化实现）
        /// </summary>
        private PanelShape GenerateMultiHolePanelShape(double panelMinX, double panelMaxX, double panelMinY, double panelMaxY,
            List<Extents3d> holes, bool isWindow)
        {
            // 对于多个洞口的复杂情况，这里先实现简化版本
            // 只处理第一个洞口，其他洞口暂时忽略
            // 在实际应用中，可以使用更复杂的几何算法来处理多个洞口

            if (holes.Count > 0)
            {
                return GenerateSingleHolePanelShape(panelMinX, panelMaxX, panelMinY, panelMaxY, holes[0], isWindow);
            }

            // 如果没有洞口，返回标准矩形
            PanelShape shape = new PanelShape();
            shape.Vertices.Add(new Point2d(panelMinX, panelMinY));
            shape.Vertices.Add(new Point2d(panelMaxX, panelMinY));
            shape.Vertices.Add(new Point2d(panelMaxX, panelMaxY));
            shape.Vertices.Add(new Point2d(panelMinX, panelMaxY));
            return shape;
        }

        /// <summary>
        /// 创建多边形板材实体
        /// </summary>
        /// <param name="shape">板材形状信息</param>
        /// <param name="ms">模型空间</param>
        /// <param name="tr">事务</param>
        /// <returns>是否成功创建</returns>
        private bool CreatePanelEntity(PanelShape shape, BlockTableRecord ms, Transaction tr)
        {
            if (shape == null || shape.Vertices == null || shape.Vertices.Count < 3)
            {
                return false;
            }

            try
            {
                using (Polyline panel = new Polyline())
                {
                    // 添加所有顶点
                    for (int i = 0; i < shape.Vertices.Count; i++)
                    {
                        panel.AddVertexAt(i, shape.Vertices[i], 0, 0, 0);
                    }

                    panel.Closed = true;

                    // 将板添加到模型空间
                    ms.AppendEntity(panel);
                    tr.AddNewlyCreatedDBObject(panel, true);
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n创建多边形板材时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取与指定X范围相交的洞口
        /// </summary>
        /// <param name="allExtents">所有洞口范围</param>
        /// <param name="minX">X范围最小值</param>
        /// <param name="maxX">X范围最大值</param>
        /// <returns>相交的洞口范围列表</returns>
        private List<Extents3d> GetIntersectingHoles(List<Extents3d> allExtents, double minX, double maxX)
        {
            List<Extents3d> intersecting = new List<Extents3d>();

            foreach (var extent in allExtents)
            {
                // 检查X方向是否有重叠
                if (!(extent.MaxPoint.X < minX - TOLERANCE || extent.MinPoint.X > maxX + TOLERANCE))
                {
                    intersecting.Add(extent);
                }
            }

            return intersecting;
        }

        /// <summary>
        /// 获取指定X范围内轮廓线的最低Y坐标（用于水平顶部矩形板）
        /// </summary>
        private double GetOutlineLowestYAtXrange(Polyline outline, double minX, double maxX)
        {
            double lowestY = double.MaxValue;
            bool foundPoint = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，检查Y坐标
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        lowestY = Math.Min(lowestY, p1.Y);
                        foundPoint = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        lowestY = Math.Min(lowestY, p2.Y);
                        foundPoint = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        lowestY = Math.Min(lowestY, y);
                        foundPoint = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        lowestY = Math.Min(lowestY, y);
                        foundPoint = true;
                    }
                }
            }

            if (foundPoint)
            {
                return lowestY;
            }
            
            // 如果没有找到点，返回一个默认值
            return double.MaxValue;
        }

        /// <summary>
        /// 获取指定X范围内轮廓线的最高Y坐标（用于确保矩形板顶部水平且不超出轮廓线）
        /// </summary>
        private double GetOutlineHighestYAtXrange(Polyline outline, double minX, double maxX)
        {
            double highestY = double.MinValue;
            bool foundPoint = false;

            // 检查每个线段与给定X范围的交点
            for (int i = 0; i < outline.NumberOfVertices; i++)
            {
                int nextIdx = (i + 1) % outline.NumberOfVertices;
                Point2d p1 = outline.GetPoint2dAt(i);
                Point2d p2 = outline.GetPoint2dAt(nextIdx);

                // 判断线段是否在X范围内
                if ((p1.X >= minX && p1.X <= maxX) || (p2.X >= minX && p2.X <= maxX) ||
                    (p1.X <= minX && p2.X >= maxX) || (p1.X >= minX && p2.X <= maxX))
                {
                    // 如果线段端点在X范围内，检查Y坐标
                    if (p1.X >= minX && p1.X <= maxX)
                    {
                        highestY = Math.Max(highestY, p1.Y);
                        foundPoint = true;
                    }
                    
                    if (p2.X >= minX && p2.X <= maxX)
                    {
                        highestY = Math.Max(highestY, p2.Y);
                        foundPoint = true;
                    }

                    // 如果线段与X范围的边界相交，计算交点的Y值
                    if ((p1.X < minX && p2.X > minX) || (p1.X > minX && p2.X < minX))
                    {
                        // 计算与minX的交点
                        double t = (minX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        highestY = Math.Max(highestY, y);
                        foundPoint = true;
                    }
                    
                    if ((p1.X < maxX && p2.X > maxX) || (p1.X > maxX && p2.X < maxX))
                    {
                        // 计算与maxX的交点
                        double t = (maxX - p1.X) / (p2.X - p1.X);
                        double y = p1.Y + t * (p2.Y - p1.Y);
                        
                        highestY = Math.Max(highestY, y);
                        foundPoint = true;
                    }
                }
            }

            if (foundPoint)
            {
                return highestY;
            }
            
            // 如果没有找到点，返回一个默认值
            return double.MinValue;
        }

        /// <summary>
        /// 获取轮廓线在指定X坐标处的Y坐标（取最高点）
        /// </summary>
        private double GetYCoordinateAtX(Polyline outline, double x)
        {
            try
            {
                double highestY = double.MinValue;
                bool foundPoint = false;

                // 检查每个线段
                for (int i = 0; i < outline.NumberOfVertices; i++)
                {
                    int nextIdx = (i + 1) % outline.NumberOfVertices;
                    Point2d p1 = outline.GetPoint2dAt(i);
                    Point2d p2 = outline.GetPoint2dAt(nextIdx);

                    // 判断线段是否与X坐标相交（使用容差比较）
                    bool p1InRange = Math.Abs(p1.X - x) < TOLERANCE;
                    bool p2InRange = Math.Abs(p2.X - x) < TOLERANCE;
                    bool lineSpansX = (p1.X <= x && p2.X >= x) || (p1.X >= x && p2.X <= x);

                    if (p1InRange || p2InRange || lineSpansX)
                    {
                        // 如果线段端点正好在X坐标处
                        if (p1InRange)
                        {
                            highestY = Math.Max(highestY, p1.Y);
                            foundPoint = true;
                        }

                        if (p2InRange)
                        {
                            highestY = Math.Max(highestY, p2.Y);
                            foundPoint = true;
                        }

                        // 如果线段跨越X坐标且不是垂直线
                        if (!p1InRange && !p2InRange && Math.Abs(p2.X - p1.X) > TOLERANCE)
                        {
                            // 计算交点的Y值
                            double t = (x - p1.X) / (p2.X - p1.X);

                            // 确保t在有效范围内
                            if (t >= -TOLERANCE && t <= 1.0 + TOLERANCE)
                            {
                                double y = p1.Y + t * (p2.Y - p1.Y);
                                highestY = Math.Max(highestY, y);
                                foundPoint = true;
                            }
                        }
                    }
                }

                if (foundPoint)
                {
                    return highestY;
                }

                // 如果没有找到交点，返回默认值
                return double.MinValue;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException($"获取X坐标{x}处的Y坐标时发生错误", ex);
            }
        }



        /// <summary>
        /// 添加窗洞口
        /// </summary>
        /// <returns>是否成功添加窗洞口</returns>
        public bool AddWindow()
        {
            // 创建选择选项
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择窗洞口(闭合多段线): ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只选择多段线
            TypedValue[] filterList = new TypedValue[1];
            filterList[0] = new TypedValue((int)DxfCode.Start, "LWPOLYLINE");
            SelectionFilter filter = new SelectionFilter(filterList);

            try
            {
                // 提示用户选择对象
                PromptSelectionResult selResult = _ed.GetSelection(selOptions, filter);
                if (selResult.Status != PromptStatus.OK)
                    return false;

                // 处理选中的对象
                bool addedAny = false;
                using (Transaction tr = _db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        SelectionSet ss = selResult.Value;
                        foreach (SelectedObject selObj in ss)
                        {
                            if (selObj != null)
                            {
                                Polyline pline = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Polyline;
                                if (pline != null && pline.Closed && pline.NumberOfVertices == 4)
                                {
                                    _windowIds.Add(selObj.ObjectId);
                                    addedAny = true;
                                }
                                else
                                {
                                    _ed.WriteMessage("\n忽略非闭合或非四边形的多段线。");
                                }
                            }
                        }
                        tr.Commit();
                    }
                    catch (System.Exception ex)
                    {
                        _ed.WriteMessage($"\n添加窗洞口时出错: {ex.Message}");
                        tr.Abort();
                        return false;
                    }
                }

                if (addedAny)
                {
                    _ed.WriteMessage($"\n已添加{_windowIds.Count}个窗洞口。");
                    return true;
                }
                else
                {
                    _ed.WriteMessage("\n未找到符合条件的窗洞口（需要是闭合的四边形多段线）。");
                    return false;
                }
            }
            finally
            {
                // SelectionFilter不需要手动释放
            }
        }

        /// <summary>
        /// 添加门洞口
        /// </summary>
        /// <returns>是否成功添加门洞口</returns>
        public bool AddDoor()
        {
            // 创建选择选项
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择门洞口(由三条线组成的多段线): ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只选择多段线
            TypedValue[] filterList = new TypedValue[1];
            filterList[0] = new TypedValue((int)DxfCode.Start, "LWPOLYLINE");
            SelectionFilter filter = new SelectionFilter(filterList);

            try
            {
                // 提示用户选择对象
                PromptSelectionResult selResult = _ed.GetSelection(selOptions, filter);
                if (selResult.Status != PromptStatus.OK)
                    return false;

                // 处理选中的对象
                bool addedAny = false;
                using (Transaction tr = _db.TransactionManager.StartTransaction())
                {
                    try
                    {
                        SelectionSet ss = selResult.Value;
                        foreach (SelectedObject selObj in ss)
                        {
                            if (selObj != null)
                            {
                                Polyline pline = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Polyline;
                                if (pline != null && !pline.Closed && pline.NumberOfVertices == 4) // 三条线，四个点(首尾不同)
                                {
                                    _doorIds.Add(selObj.ObjectId);
                                    addedAny = true;
                                }
                                else
                                {
                                    _ed.WriteMessage("\n忽略闭合或非三线段的多段线。");
                                }
                            }
                        }
                        tr.Commit();
                    }
                    catch (System.Exception ex)
                    {
                        _ed.WriteMessage($"\n添加门洞口时出错: {ex.Message}");
                        tr.Abort();
                        return false;
                    }
                }

                if (addedAny)
                {
                    _ed.WriteMessage($"\n已添加{_doorIds.Count}个门洞口。");
                    return true;
                }
                else
                {
                    _ed.WriteMessage("\n未找到符合条件的门洞口（需要是非闭合的三线段多段线）。");
                    return false;
                }
            }
            finally
            {
                // SelectionFilter不需要手动释放
            }
        }

        #region 选择方法
        /// <summary>
        /// 设置轮廓线
        /// </summary>
        /// <returns>是否成功选择轮廓线</returns>
        public bool SetOutline()
        {
            PromptEntityResult result = _ed.GetEntity("\n请选择轮廓线: ");
            if (result.Status != PromptStatus.OK)
                return false;

            using (Transaction tr = _db.TransactionManager.StartTransaction())
            {
                try
                {
                    Polyline outline = tr.GetObject(result.ObjectId, OpenMode.ForRead) as Polyline;
                    if (outline == null)
                    {
                        _ed.WriteMessage("\n请选择有效的多段线!");
                        tr.Abort();
                        return false;
                    }

                    // 存储ObjectId而不是直接引用对象
                    _outlineId = result.ObjectId;
                    tr.Commit();
                    return true;
                }
                catch (System.Exception ex)
                {
                    _ed.WriteMessage("\n选择轮廓线时出错: {0}", ex.Message);
                    tr.Abort();
                    return false;
                }
            }
        }

        /// <summary>
        /// 清除选择的门窗和轮廓
        /// </summary>
        public void ClearSelection()
        {
            _outlineId = ObjectId.Null;
            _windowIds.Clear();
            _doorIds.Clear();
        }
        #endregion
        #endregion
    }
}