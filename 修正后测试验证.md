# 修正后逻辑测试验证

## 测试场景设计

### 场景1：真正的左侧重叠
**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1200mm
- 板材位置: 第一块板材 [0, 1200]
- 窗户洞口: (1000,1000) 到 (1800,2000)
- 窗户间隙: 50mm

**重叠分析**:
- 板材范围: [0, 1200]
- 洞口范围: [1000, 1800]  
- 重叠部分: [1000, 1200] (板材右侧200mm与洞口重叠)
- 重叠类型: LeftOverlap (板材左侧在洞口外，右侧在洞口内)

**修正后预期结果**:
- 去掉重叠部分: [1000-50, 1200] = [950, 1200]
- 保留左侧部分: [0, 950]
- 生成矩形板材: 左下(0,0) → 右下(950,0) → 右上(950,2700) → 左上(0,2700)

### 场景2：真正的右侧重叠  
**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1200mm
- 板材位置: 从右往左，最后一块板材 [1800, 3000]
- 窗户洞口: (1500,1000) 到 (2200,2000)
- 窗户间隙: 50mm

**重叠分析**:
- 板材范围: [1800, 3000]
- 洞口范围: [1500, 2200]
- 重叠部分: [1800, 2200] (板材左侧400mm与洞口重叠)
- 重叠类型: RightOverlap (板材左侧在洞口内，右侧在洞口外)

**修正后预期结果**:
- 去掉重叠部分: [1800, 2200+50] = [1800, 2250]
- 保留右侧部分: [2250, 3000]
- 生成矩形板材: 左下(2250,0) → 右下(3000,0) → 右上(3000,2700) → 左上(2250,2700)

### 场景3：洞口完全在板材内
**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1500mm
- 板材位置: 第一块板材 [0, 1500]
- 窗户洞口: (200,1000) 到 (800,2000)
- 窗户间隙: 50mm

**重叠分析**:
- 板材范围: [0, 1500]
- 洞口范围: [200, 800]
- 洞口完全在板材内
- 重叠类型: HoleCompletelyInside

**修正后预期结果**:
- 生成"凸"字形板材，中间有凹槽
- 凹槽范围: X[150, 850], Y[950, 2050] (加上50mm间隙)
- 顶点顺序: 逆时针，从左下角开始

### 场景4：完全包含情况（原有逻辑）
**设置**:
- 板材范围: [1000, 1500]
- 洞口范围: [800, 1800]
- 板材完全在洞口内

**预期结果**:
- 重叠类型: CompletelyInside
- 使用原有分段逻辑，在洞口上下生成两段矩形板材

## 验证检查点

### 1. 重叠类型判断验证
```csharp
// 测试 GetOverlapType 方法
OverlapType type1 = GetOverlapType(0, 1200, 1000, 1800);     // 应该返回 LeftOverlap
OverlapType type2 = GetOverlapType(1800, 3000, 1500, 2200); // 应该返回 RightOverlap  
OverlapType type3 = GetOverlapType(0, 1500, 200, 800);      // 应该返回 HoleCompletelyInside
OverlapType type4 = GetOverlapType(1000, 1500, 800, 1800);  // 应该返回 CompletelyInside
```

### 2. 形状生成验证
- **左侧重叠**: 检查生成的矩形左边界是否正确调整
- **右侧重叠**: 检查生成的矩形右边界是否正确调整
- **洞口在内**: 检查凹槽位置和尺寸是否正确

### 3. 边界情况测试
- 重叠部分很小的情况
- 重叠部分很大的情况
- 调整后板材宽度不足最小要求的情况

## 预期控制台输出

```
开始生成矩形板...
检测到左侧重叠洞口，去掉重叠部分，保留右侧
成功生成1个矩形板材 (调整后尺寸: 950x2700)

检测到右侧重叠洞口，去掉重叠部分，保留左侧  
成功生成1个矩形板材 (调整后尺寸: 750x2700)

检测到洞口完全在板材内，生成凹槽形状
成功生成1个非矩形板材 (凸字形)

总计生成3个板材
```

## 成功标准

- [ ] 左侧重叠正确去掉右侧重叠部分
- [ ] 右侧重叠正确去掉左侧重叠部分  
- [ ] 洞口在内正确生成凹槽形状
- [ ] 生成的板材与洞口保持适当间隙
- [ ] 板材尺寸计算正确
- [ ] 不生成过小的板材
- [ ] 原有完全包含逻辑保持不变

通过这些测试可以验证修正后的逻辑是否正确处理各种重叠情况。
