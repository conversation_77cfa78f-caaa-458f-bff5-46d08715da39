# 部分重叠处理功能测试用例

## 测试场景设计

### 场景1：左侧重叠测试
**描述**: 板材左侧与窗户洞口重叠

**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1200mm
- 窗户洞口: (800,1000) 到 (1500,2000)
- 排版方向: 从左到右

**预期结果**:
- 第一块板材应该去掉左侧重叠部分，保留右侧部分
- 新的板材范围：从洞口右边界(1500+间隙)到原板材右边界(1200)
- 由于调整后宽度可能不足，可能不生成板材或生成较窄的板材

### 场景2：右侧重叠测试
**描述**: 板材右侧与窗户洞口重叠

**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1200mm
- 窗户洞口: (1000,1000) 到 (1700,2000)
- 排版方向: 从右到左

**预期结果**:
- 最后一块板材应该去掉右侧重叠部分，保留左侧部分
- 新的板材范围：从原板材左边界到洞口左边界(1000-间隙)

### 场景3：洞口完全在板材内
**描述**: 窗户洞口完全在板材范围内

**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1500mm
- 窗户洞口: (200,1000) 到 (800,2000)
- 排版方向: 从左到右

**预期结果**:
- 第一块板材应该生成"凸"字形状
- 中间凹槽对应窗户洞口位置

### 场景4：门洞口测试
**描述**: 板材与门洞口部分重叠

**设置**:
- 轮廓线: 矩形 (0,0) 到 (3000,2700)
- 板材宽度: 1200mm
- 门洞口: (800,0) 到 (1500,2100) (注意门是开放的多段线)
- 排版方向: 从左到右

**预期结果**:
- 第一块板材应该去掉左侧重叠部分，保留右侧部分
- 新的板材范围：从门洞口右边界(1500+间隙)到原板材右边界(1200)
- 由于门洞口较宽，可能导致板材宽度不足而不生成

## 测试步骤

### 准备工作
1. 在AutoCAD中创建测试轮廓线
2. 创建相应的门窗洞口多段线
3. 启动矩形板排版工具

### 执行测试
1. 选择轮廓线
2. 设置板材参数（宽度、间隙等）
3. 添加门窗洞口
4. 选择排版方向
5. 生成板材

### 验证结果
1. 检查生成的板材数量是否正确
2. 验证非矩形板材的形状是否符合预期
3. 确认板材与洞口之间保持适当间隙
4. 检查板材是否完全在轮廓线内

## 预期输出示例

### 控制台输出
```
开始生成矩形板...
检测到部分重叠洞口，生成非矩形板材
成功生成3个板材（包含1个非矩形板材）
```

### 图形输出
- 标准矩形板材：4个顶点的闭合多段线
- 非矩形板材：6-12个顶点的闭合多段线，具有凹槽形状

## 错误处理测试

### 异常情况1：洞口过大
**设置**: 洞口尺寸超过板材尺寸
**预期**: 系统应该跳过该板材位置或生成警告

### 异常情况2：间隙过大
**设置**: 间隙设置导致板材尺寸过小
**预期**: 系统应该跳过生成过小的板材

### 异常情况3：多个重叠洞口
**设置**: 多个洞口同时与板材重叠
**预期**: 系统应该处理第一个洞口，其他洞口按原有逻辑处理

## 性能测试

### 大量洞口测试
- 创建包含20+个洞口的复杂场景
- 测试生成时间是否在可接受范围内
- 验证内存使用是否正常

### 复杂轮廓测试
- 使用不规则轮廓线
- 测试算法在复杂几何情况下的稳定性

## 回归测试

确保新功能不影响原有功能：

1. **标准矩形板材**: 无洞口情况下应该生成标准矩形
2. **完全包含情况**: 板材完全在洞口内应该按原有逻辑分段
3. **无重叠情况**: 板材与洞口无重叠应该生成标准矩形

## 测试通过标准

- [ ] 所有测试场景都能正确生成预期形状
- [ ] 生成的板材与洞口保持适当间隙
- [ ] 非矩形板材的顶点顺序正确（顺时针）
- [ ] 系统性能没有明显下降
- [ ] 原有功能保持正常工作
- [ ] 错误处理机制工作正常

通过以上测试用例可以全面验证部分重叠处理功能的正确性和稳定性。
