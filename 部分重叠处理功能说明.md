# 矩形板排版工具 - 部分重叠处理功能

## 功能概述

本次更新为矩形板排版工具添加了处理板材与洞口部分重叠的功能，能够自动生成非矩形的板材形状来避开洞口区域，解决了原有算法只能处理板材完全在洞口内情况的局限性。

## 新增功能特性

### 1. 重叠类型检测

系统现在能够识别以下几种板材与洞口的重叠情况：

- **无重叠 (None)**: 板材与洞口完全不重叠
- **完全包含 (CompletelyInside)**: 板材完全在洞口内（原有逻辑）
- **左侧重叠 (LeftOverlap)**: 板材左侧与洞口重叠，右侧在洞口外
- **右侧重叠 (RightOverlap)**: 板材右侧与洞口重叠，左侧在洞口外
- **洞口完全在内 (HoleCompletelyInside)**: 洞口完全在板材内部
- **完全重叠 (CompleteOverlap)**: 板材和洞口范围基本相同

### 2. 非矩形板材生成

根据不同的重叠类型，系统会生成相应的非矩形板材形状：

#### 左侧重叠情况
- 生成右侧有凹槽的板材形状
- 凹槽位置对应洞口位置加上间隙
- 适用于板材左侧进入洞口范围的情况

#### 右侧重叠情况
- 生成左侧有凹槽的板材形状
- 凹槽位置对应洞口位置加上间隙
- 适用于板材右侧进入洞口范围的情况

#### 洞口完全在板材内部
- 生成中间有凹槽的"凸"字形板材
- 凹槽完全包围洞口并保持适当间隙
- 适用于洞口完全位于板材范围内的情况

### 3. 智能间隙处理

- **窗户间隙**: 使用 `_windowGap` 参数在洞口周围保持间隙
- **门间隙**: 使用 `_doorTopGap` 参数，门的底部直接到地面
- **自动调整**: 确保生成的凹槽不超出板材原有范围

## 技术实现

### 核心算法

1. **重叠检测算法** (`GetOverlapType`)
   - 使用容差比较避免浮点精度问题
   - 精确判断板材与洞口的相对位置关系

2. **形状生成算法** (`GeneratePanelShape`)
   - 根据重叠类型选择相应的形状生成策略
   - 支持单个洞口和多个洞口的处理

3. **多边形创建** (`CreatePanelEntity`)
   - 使用AutoCAD的Polyline对象创建复杂多边形
   - 自动处理顶点顺序和闭合

### 代码结构

```csharp
// 重叠类型枚举
private enum OverlapType
{
    None, CompletelyInside, LeftOverlap, 
    RightOverlap, HoleCompletelyInside, CompleteOverlap
}

// 板材形状信息类
private class PanelShape
{
    public List<Point2d> Vertices { get; set; }
    public bool IsRectangular { get; set; }
}
```

## 使用方法

### 基本使用

1. 按照原有流程设置轮廓线、板材参数
2. 添加门窗洞口（支持部分重叠的洞口）
3. 选择排版方向并生成板材
4. 系统会自动检测重叠情况并生成相应形状

### 参数设置

- **窗户间隙**: 控制窗户洞口周围的间隙大小
- **门顶间隙**: 控制门洞口顶部的间隙大小
- **最小板材高度**: 确保生成的板材具有足够的高度

## 兼容性说明

### 向后兼容

- 原有的矩形板材生成逻辑完全保留
- 只有在检测到部分重叠时才启用新功能
- 不影响现有项目的使用

### 性能优化

- 只对相交的洞口进行重叠检测
- 使用高效的几何算法减少计算量
- 保持原有的性能水平

## 注意事项

1. **复杂洞口**: 当前版本对多个洞口的处理采用简化策略，优先处理第一个洞口
2. **最小尺寸**: 生成的板材必须满足最小高度要求
3. **间隙设置**: 合理设置间隙参数以确保板材与洞口不冲突

## 后续扩展

- 支持更复杂的多洞口处理算法
- 添加板材形状优化功能
- 支持自定义板材形状模板
- 增加可视化预览功能

## 测试建议

建议在以下场景下测试新功能：

1. 单个窗户与板材左侧重叠
2. 单个窗户与板材右侧重叠
3. 窗户完全在板材内部
4. 门洞口的各种重叠情况
5. 多个洞口同时存在的复杂场景

通过这些测试可以验证新功能的正确性和稳定性。
